<template>
  <view class="login-container">
    <!-- 内容区域 -->
    <view class="content">
      <!-- 品牌logo -->
      <view class="logo">
        <u-icon name="account" size="140" color="#ffffff" />
      </view>
      
      <!-- 标题区域 -->
      <view class="title-area">
        <text class="main-title">疾控考试系统</text>
        <text class="sub-title">医护任职资格考试平台</text>
      </view>
      
      <!-- 微信登录按钮 -->
      <view class="login-btn-container">
        <u-button
          type="primary"
          :disabled="!agreedToTerms || isLoading"
          :loading="isLoading"
          loadingText="登录中..."
          :customStyle="loginButtonStyle"
          shape="round"
          size="large"
          :throttleTime="1000"
          @click="handleWxLogin"
        >
          <view class="btn-content">
            <u-icon name="weixin-fill" size="20" color="#ffffff" :customStyle="wechatIconStyle" />
            <text class="btn-text">微信授权登录</text>
          </view>
        </u-button>
      </view>
      
      <!-- 协议确认 -->
      <view class="agreement">
        <u-checkbox-group
          v-model="agreementValue"
          :disabled="isLoading"
          activeColor="#ffffff"
          inactiveColor="rgba(255,255,255,0.3)"
          iconColor="#348AC7"
          size="22"
          iconSize="16"
          shape="square"
          labelColor="#ffffff"
          labelSize="24"
          placement="row"
          @change="handleAgreementChange"
        >
          <u-checkbox
            name="agreed"
            label="我已阅读并同意"
            activeColor="#ffffff"
            iconColor="#348AC7"
          />
        </u-checkbox-group>
        <view class="agreement-links">
          <text class="link" @click="showUserAgreement">《用户服务协议》</text>
          <text class="agreement-text">和</text>
          <text class="link" @click="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>

    <!-- 用户协议模态框 -->
    <u-modal
      v-model="showUserAgreementModal"
      title="用户服务协议"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showUserAgreementModal = false"
    >
      <view class="modal-content">
        <u-text
          :text="userAgreementContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </u-modal>

    <!-- 隐私政策模态框 -->
    <u-modal
      v-model="showPrivacyPolicyModal"
      title="隐私政策"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showPrivacyPolicyModal = false"
    >
      <view class="modal-content">
        <u-text
          :text="privacyPolicyContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </u-modal>

    <!-- Toast 消息提示 -->
    <u-toast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/src/stores/modules/user'
import { wxLogin } from '@/src/api/modules/user'
import type { LoginParams, UserInfo } from '@/src/types/api'

// ==================== Interfaces ====================
interface ToastInstance {
  show: (options: {
    title: string
    type?: 'success' | 'error' | 'warning' | 'info'
    duration?: number
  }) => void
}

// ==================== Store ====================
const userStore = useUserStore()
const { profile } = storeToRefs(userStore)
const { setProfile } = userStore

// ==================== 响应式数据 ====================
/** 协议确认值（数组形式，符合u-checkbox-group要求） */
const agreementValue = ref<string[]>([])
/** 是否同意用户协议（计算属性） */
const agreedToTerms = computed(() => agreementValue.value.includes('agreed'))
/** 登录加载状态 */
const isLoading = ref<boolean>(false)
/** 显示用户协议模态框 */
const showUserAgreementModal = ref<boolean>(false)
/** 显示隐私政策模态框 */
const showPrivacyPolicyModal = ref<boolean>(false)

// ==================== Toast 引用 ====================
const toastRef = ref<ToastInstance | null>(null)

// ==================== 设计系统 ====================
/** 字体尺寸 - 使用px单位，符合uview-plus规范 */
const modalTextSize = computed(() => 14)        // 模态框文字

/** 行高 */
const modalTextLineHeight = computed(() => 22)

// ==================== 样式计算属性 ====================
/** 微信图标样式 */
const wechatIconStyle = computed(() => ({
  marginRight: '8rpx',
}))

/** 登录按钮样式 */
const loginButtonStyle = computed(() => ({
  width: '80%',
  height: '88rpx',
  backgroundColor: agreedToTerms.value ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
  borderRadius: '44rpx',
  border: 'none',
  transition: 'all 0.3s ease',
}))

/** 用户协议内容 */
const userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`)

/** 隐私政策内容 */
const privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`)

// ==================== 事件处理 ====================
/**
 * 处理协议确认变更
 * @param values 选中的值数组
 */
function handleAgreementChange(values: string[]): void {
  agreementValue.value = values
}

/**
 * 显示用户服务协议
 */
function showUserAgreement(): void {
  showUserAgreementModal.value = true
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy(): void {
  showPrivacyPolicyModal.value = true
}

/**
 * 显示Toast消息
 * @param title 消息标题
 * @param type 消息类型
 */
function showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
  if (toastRef.value) {
    toastRef.value.show({
      title,
      type,
      duration: type === 'success' ? 1500 : 2000,
    })
  }
}

/**
 * 微信授权登录
 */
async function handleWxLogin(): Promise<void> {
  // 检查协议同意状态
  if (!agreedToTerms.value) {
    showToast('请先同意用户协议', 'warning')
    return
  }

  isLoading.value = true

  try {
    // 调用微信登录获取code
    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      })
    })

    // 构造登录参数
    const loginParams: LoginParams = {
      code: loginResult.code,
    }

    // 调用后端登录接口
    const userInfo: UserInfo = await wxLogin(loginParams)

    // 保存用户信息到Store
    setProfile(userInfo)

    // 登录成功提示
    showToast('登录成功', 'success')

    // 根据用户状态进行页面跳转
    setTimeout(() => {
      navigateByUserStatus(userInfo.status)
    }, 1500)

  } catch (error) {
    console.error('微信登录失败:', error)
    showToast('登录失败，请重试', 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * 根据用户状态进行页面跳转
 * @param status 用户状态
 */
function navigateByUserStatus(status: UserInfo['status']): void {
  switch (status) {
    case 'approved':
      // 已审核通过的正式用户，跳转到信息中心
      uni.reLaunch({ url: '/pages/info/info' })
      break
    case 'pending':
      // 待审核用户，跳转到个人中心查看审核状态
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'rejected':
      // 审核未通过用户，跳转到个人中心修改资料
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'incomplete':
    default:
      // 未提交资料的新用户，跳转到注册页面
      uni.navigateTo({ url: '/pages/register/register' })
      break
  }
}
</script>

<style lang="scss" scoped>
// 导入项目设计系统变量
@import '@/src/styles/variables.scss';

/* ==================== 页面全局设置 ==================== */
page {
  height: 100%;
}

/* ==================== 主容器 ==================== */
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #1976D2 0%, #1565C0 25%, #0D47A1 50%, #0277BD 75%, #01579B 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 5vh;
  padding-bottom: 80rpx;
  padding-left: 0;
  padding-right: 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* 添加装饰性背景元素 */
.login-container::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 20s ease-in-out infinite;
}

.login-container::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -10%;
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
  border-radius: 50%;
  animation: float 25s ease-in-out infinite reverse;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 医疗装饰元素 */
.login-container {
  /* 添加医疗十字图案 */
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

/* ==================== 内容区域 ==================== */
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  justify-content: space-between;
  max-width: 600rpx;
  position: relative;
  z-index: 10;
}

/* ==================== Logo区域 ==================== */
.logo {
  width: 280rpx;
  height: 280rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50rpx;
  flex-shrink: 0;
}

/* ==================== 标题区域 ==================== */
.title-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 200rpx; /* 进一步增加标题与按钮间距，让按钮更靠下 */
  flex-shrink: 0;
}

.main-title {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.sub-title {
  font-size: 32rpx;
  color: #ffffff;
}

/* ==================== 登录按钮区域 ==================== */
.login-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx; /* 减少按钮与协议间距 */
  flex-shrink: 0;
}

.btn-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  color: #ffffff;
  font-size: 32rpx;
  margin-left: 16rpx;
}

/* ==================== 协议区域 ==================== */
.agreement {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: auto; /* 将协议区域推到底部 */
}

.agreement-links {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #ffffff;
  margin: 0 8rpx;
}

.link {
  font-size: 24rpx;
  color: #ffffff;
  text-decoration: underline;
}

/* ==================== 复选框样式优化 ==================== */
:deep(.u-checkbox-group) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.u-checkbox) {
  display: flex;
  align-items: center;
}

:deep(.u-checkbox__icon-wrap) {
  margin-right: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.6) !important;
  border-radius: 4rpx !important;
  background-color: transparent !important;
  transition: all 0.3s ease !important;
}

/* 复选框选中状态 */
:deep(.u-checkbox--checked .u-checkbox__icon-wrap) {
  background-color: #ffffff !important;
  border-color: #ffffff !important;
}

/* 复选框图标 */
:deep(.u-checkbox__icon) {
  color: #000000 !important;
  font-weight: 900 !important;
  font-size: 20rpx !important;
  transform: scale(1.2) !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 复选框未选中时的图标隐藏 */
:deep(.u-checkbox:not(.u-checkbox--checked) .u-checkbox__icon) {
  display: none !important;
}

/* ==================== 模态框内容 ==================== */
.modal-content {
  padding: $spacing-lg 0;
  max-height: 600rpx;
  overflow-y: auto;
  line-height: 1.6;
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
  .logo {
    width: 200rpx;
    height: 200rpx;
  }
  
  .main-title {
    font-size: 44rpx;
  }
  
  .sub-title {
    font-size: 28rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .content {
    padding: 0 $spacing-md;
  }
  
  .logo {
    width: 180rpx;
    height: 180rpx;
  }
  
  .main-title {
    font-size: 40rpx;
  }
  
  .sub-title {
    font-size: 26rpx;
  }
}
</style>
