{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.**********/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 内容区域 -->\n    <view class=\"content\">\n      <!-- 品牌logo -->\n      <view class=\"logo\">\n        <u-icon name=\"account\" size=\"140\" color=\"#ffffff\" />\n      </view>\n      \n      <!-- 标题区域 -->\n      <view class=\"title-area\">\n        <text class=\"main-title\">疾控考试系统</text>\n        <text class=\"sub-title\">医护任职资格考试平台</text>\n      </view>\n      \n      <!-- 微信登录按钮 -->\n      <view class=\"login-btn-container\">\n        <u-button\n          type=\"primary\"\n          :disabled=\"!agreedToTerms || isLoading\"\n          :loading=\"isLoading\"\n          loadingText=\"登录中...\"\n          :customStyle=\"loginButtonStyle\"\n          shape=\"round\"\n          size=\"large\"\n          :throttleTime=\"1000\"\n          @click=\"handleWxLogin\"\n        >\n          <view class=\"btn-content\">\n            <u-icon name=\"weixin-fill\" size=\"20\" color=\"#ffffff\" :customStyle=\"wechatIconStyle\" />\n            <text class=\"btn-text\">微信授权登录</text>\n          </view>\n        </u-button>\n      </view>\n      \n      <!-- 协议确认 -->\n      <view class=\"agreement\">\n        <u-checkbox-group\n          v-model=\"agreementValue\"\n          :disabled=\"isLoading\"\n          activeColor=\"#ffffff\"\n          inactiveColor=\"rgba(255,255,255,0.3)\"\n          iconColor=\"#348AC7\"\n          size=\"22\"\n          iconSize=\"16\"\n          shape=\"square\"\n          labelColor=\"#ffffff\"\n          labelSize=\"24\"\n          placement=\"row\"\n          @change=\"handleAgreementChange\"\n        >\n          <u-checkbox\n            name=\"agreed\"\n            label=\"我已阅读并同意\"\n            activeColor=\"#ffffff\"\n            iconColor=\"#348AC7\"\n          />\n        </u-checkbox-group>\n        <view class=\"agreement-links\">\n          <text class=\"link\" @click=\"showUserAgreement\">《用户服务协议》</text>\n          <text class=\"agreement-text\">和</text>\n          <text class=\"link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 用户协议模态框 -->\n    <u-modal\n      v-model=\"showUserAgreementModal\"\n      title=\"用户服务协议\"\n      :showCancelButton=\"false\"\n      confirmText=\"我知道了\"\n      @confirm=\"showUserAgreementModal = false\"\n    >\n      <view class=\"modal-content\">\n        <u-text\n          :text=\"userAgreementContent\"\n          :size=\"modalTextSize\"\n          color=\"#212121\"\n          :lineHeight=\"modalTextLineHeight\"\n        />\n      </view>\n    </u-modal>\n\n    <!-- 隐私政策模态框 -->\n    <u-modal\n      v-model=\"showPrivacyPolicyModal\"\n      title=\"隐私政策\"\n      :showCancelButton=\"false\"\n      confirmText=\"我知道了\"\n      @confirm=\"showPrivacyPolicyModal = false\"\n    >\n      <view class=\"modal-content\">\n        <u-text\n          :text=\"privacyPolicyContent\"\n          :size=\"modalTextSize\"\n          color=\"#212121\"\n          :lineHeight=\"modalTextLineHeight\"\n        />\n      </view>\n    </u-modal>\n\n    <!-- Toast 消息提示 -->\n    <u-toast ref=\"toastRef\" />\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed } from 'vue'\nimport { storeToRefs } from 'pinia'\nimport { useUserStore } from '@/src/stores/modules/user'\nimport { wxLogin } from '@/src/api/modules/user'\nimport type { LoginParams, UserInfo } from '@/src/types/api'\n\n// ==================== Interfaces ====================\ninterface ToastInstance {\n  show: (options: {\n    title: string\n    type?: 'success' | 'error' | 'warning' | 'info'\n    duration?: number\n  }) => void\n}\n\n// ==================== Store ====================\nconst userStore = useUserStore()\nconst { profile } = storeToRefs(userStore)\nconst { setProfile } = userStore\n\n// ==================== 响应式数据 ====================\n/** 协议确认值（数组形式，符合u-checkbox-group要求） */\nconst agreementValue = ref<string[]>([])\n/** 是否同意用户协议（计算属性） */\nconst agreedToTerms = computed(() => agreementValue.value.includes('agreed'))\n/** 登录加载状态 */\nconst isLoading = ref<boolean>(false)\n/** 显示用户协议模态框 */\nconst showUserAgreementModal = ref<boolean>(false)\n/** 显示隐私政策模态框 */\nconst showPrivacyPolicyModal = ref<boolean>(false)\n\n// ==================== Toast 引用 ====================\nconst toastRef = ref<ToastInstance | null>(null)\n\n// ==================== 设计系统 ====================\n/** 字体尺寸 - 使用px单位，符合uview-plus规范 */\nconst modalTextSize = computed(() => 14)        // 模态框文字\n\n/** 行高 */\nconst modalTextLineHeight = computed(() => 22)\n\n// ==================== 样式计算属性 ====================\n/** 微信图标样式 */\nconst wechatIconStyle = computed(() => ({\n  marginRight: '8rpx',\n}))\n\n/** 登录按钮样式 */\nconst loginButtonStyle = computed(() => ({\n  width: '80%',\n  height: '88rpx',\n  backgroundColor: agreedToTerms.value ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n  borderRadius: '44rpx',\n  border: 'none',\n  transition: 'all 0.3s ease',\n}))\n\n/** 用户协议内容 */\nconst userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台\n2. 用户需提供真实有效的个人信息\n3. 考试过程中需遵守相关规定\n4. 系统会记录用户的学习和考试行为\n5. 用户信息将严格保密，仅用于考试管理\n\n详细协议内容请联系管理员获取。`)\n\n/** 隐私政策内容 */\nconst privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录\n2. 信息用途：身份验证、考试管理、成绩统计\n3. 信息保护：采用加密存储，严格权限控制\n4. 信息共享：仅与相关机构共享必要信息\n5. 用户权利：可查看、修改个人信息\n\n详细政策内容请联系管理员获取。`)\n\n// ==================== 事件处理 ====================\n/**\n * 处理协议确认变更\n * @param values 选中的值数组\n */\nfunction handleAgreementChange(values: string[]): void {\n  agreementValue.value = values\n}\n\n/**\n * 显示用户服务协议\n */\nfunction showUserAgreement(): void {\n  showUserAgreementModal.value = true\n}\n\n/**\n * 显示隐私政策\n */\nfunction showPrivacyPolicy(): void {\n  showPrivacyPolicyModal.value = true\n}\n\n/**\n * 显示Toast消息\n * @param title 消息标题\n * @param type 消息类型\n */\nfunction showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {\n  if (toastRef.value) {\n    toastRef.value.show({\n      title,\n      type,\n      duration: type === 'success' ? 1500 : 2000,\n    })\n  }\n}\n\n/**\n * 微信授权登录\n */\nasync function handleWxLogin(): Promise<void> {\n  // 检查协议同意状态\n  if (!agreedToTerms.value) {\n    showToast('请先同意用户协议', 'warning')\n    return\n  }\n\n  isLoading.value = true\n\n  try {\n    // 调用微信登录获取code\n    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {\n      uni.login({\n        provider: 'weixin',\n        success: resolve,\n        fail: reject,\n      })\n    })\n\n    // 构造登录参数\n    const loginParams: LoginParams = {\n      code: loginResult.code,\n    }\n\n    // 调用后端登录接口\n    const userInfo: UserInfo = await wxLogin(loginParams)\n\n    // 保存用户信息到Store\n    setProfile(userInfo)\n\n    // 登录成功提示\n    showToast('登录成功', 'success')\n\n    // 根据用户状态进行页面跳转\n    setTimeout(() => {\n      navigateByUserStatus(userInfo.status)\n    }, 1500)\n\n  } catch (error) {\n    uni.__f__('error','at pages/login/login.vue:265','微信登录失败:', error)\n    showToast('登录失败，请重试', 'error')\n  } finally {\n    isLoading.value = false\n  }\n}\n\n/**\n * 根据用户状态进行页面跳转\n * @param status 用户状态\n */\nfunction navigateByUserStatus(status: UserInfo['status']): void {\n  switch (status) {\n    case 'approved':\n      // 已审核通过的正式用户，跳转到信息中心\n      uni.reLaunch({ url: '/pages/info/info' })\n      break\n    case 'pending':\n      // 待审核用户，跳转到个人中心查看审核状态\n      uni.reLaunch({ url: '/pages/profile/profile' })\n      break\n    case 'rejected':\n      // 审核未通过用户，跳转到个人中心修改资料\n      uni.reLaunch({ url: '/pages/profile/profile' })\n      break\n    case 'incomplete':\n    default:\n      // 未提交资料的新用户，跳转到注册页面\n      uni.navigateTo({ url: '/pages/register/register' })\n      break\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n// 导入项目设计系统变量\n@import '@/src/styles/variables.scss';\n\n/* ==================== 页面全局设置 ==================== */\npage {\n  height: 100%;\n}\n\n/* ==================== 主容器 ==================== */\n.login-container {\n  height: 100vh;\n  background: linear-gradient(135deg, #1976D2 0%, #1565C0 25%, #0D47A1 50%, #0277BD 75%, #01579B 100%);\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: flex-start;\n  padding-top: 5vh;\n  padding-bottom: 80rpx;\n  padding-left: 0;\n  padding-right: 0;\n  box-sizing: border-box;\n  overflow: hidden;\n}\n\n/* 添加装饰性背景元素 */\n.login-container::before {\n  content: '';\n  position: absolute;\n  top: -30%;\n  right: -15%;\n  width: 120%;\n  height: 120%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 60%);\n  border-radius: 50%;\n  animation: float 30s ease-in-out infinite;\n}\n\n.login-container::after {\n  content: '';\n  position: absolute;\n  bottom: -20%;\n  left: -15%;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.03) 0%, transparent 50%);\n  border-radius: 50%;\n  animation: float 35s ease-in-out infinite reverse;\n}\n\n/* 浮动动画 */\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-20px) rotate(180deg);\n  }\n}\n\n/* 医疗装饰元素 - 简化版本 */\n.login-container {\n  /* 添加微妙的装饰点 */\n  background-image:\n    radial-gradient(circle at 15% 85%, rgba(255, 255, 255, 0.03) 0%, transparent 40%),\n    radial-gradient(circle at 85% 15%, rgba(255, 255, 255, 0.03) 0%, transparent 40%);\n}\n\n/* ==================== 内容区域 ==================== */\n.content {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n  justify-content: space-between;\n  max-width: 600rpx;\n  position: relative;\n  z-index: 10;\n}\n\n/* ==================== Logo区域 ==================== */\n.logo {\n  width: 280rpx;\n  height: 280rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 50rpx;\n  flex-shrink: 0;\n}\n\n/* ==================== 标题区域 ==================== */\n.title-area {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 200rpx; /* 进一步增加标题与按钮间距，让按钮更靠下 */\n  flex-shrink: 0;\n}\n\n.main-title {\n  font-size: 48rpx;\n  color: #ffffff;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n}\n\n.sub-title {\n  font-size: 32rpx;\n  color: #ffffff;\n}\n\n/* ==================== 登录按钮区域 ==================== */\n.login-btn-container {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 60rpx; /* 减少按钮与协议间距 */\n  flex-shrink: 0;\n}\n\n.btn-content {\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.btn-text {\n  color: #ffffff;\n  font-size: 32rpx;\n  margin-left: 16rpx;\n}\n\n/* ==================== 协议区域 ==================== */\n.agreement {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  margin-top: auto; /* 将协议区域推到底部 */\n}\n\n.agreement-links {\n  margin-top: 16rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.agreement-text {\n  font-size: 24rpx;\n  color: #ffffff;\n  margin: 0 8rpx;\n}\n\n.link {\n  font-size: 24rpx;\n  color: #ffffff;\n  text-decoration: underline;\n}\n\n/* ==================== 复选框样式优化 ==================== */\n:deep(.u-checkbox-group) {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n:deep(.u-checkbox) {\n  display: flex;\n  align-items: center;\n}\n\n:deep(.u-checkbox__icon-wrap) {\n  margin-right: 16rpx;\n  border: 2rpx solid rgba(255, 255, 255, 0.6) !important;\n  border-radius: 4rpx !important;\n  background-color: transparent !important;\n  transition: all 0.3s ease !important;\n}\n\n/* 复选框选中状态 */\n:deep(.u-checkbox--checked .u-checkbox__icon-wrap) {\n  background-color: #ffffff !important;\n  border-color: #ffffff !important;\n}\n\n/* 复选框图标 */\n:deep(.u-checkbox__icon) {\n  color: #000000 !important;\n  font-weight: 900 !important;\n  font-size: 20rpx !important;\n  transform: scale(1.2) !important;\n  position: relative !important;\n  z-index: 2 !important;\n}\n\n/* 复选框未选中时的图标隐藏 */\n:deep(.u-checkbox:not(.u-checkbox--checked) .u-checkbox__icon) {\n  display: none !important;\n}\n\n/* ==================== 模态框内容 ==================== */\n.modal-content {\n  padding: $spacing-lg 0;\n  max-height: 600rpx;\n  overflow-y: auto;\n  line-height: 1.6;\n}\n\n/* ==================== 响应式适配 ==================== */\n@media screen and (max-width: 750rpx) {\n  .logo {\n    width: 200rpx;\n    height: 200rpx;\n  }\n  \n  .main-title {\n    font-size: 44rpx;\n  }\n  \n  .sub-title {\n    font-size: 28rpx;\n  }\n}\n\n@media screen and (max-width: 600rpx) {\n  .content {\n    padding: 0 $spacing-md;\n  }\n  \n  .logo {\n    width: 180rpx;\n    height: 180rpx;\n  }\n  \n  .main-title {\n    font-size: 40rpx;\n  }\n  \n  .sub-title {\n    font-size: 26rpx;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "computed", "uni", "wxL<PERSON>in"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HA,UAAM,YAAYA,wBAAAA;AACEC,kBAAAA,YAAY,SAAS;AACnC,UAAA,EAAE,WAAe,IAAA;AAIjB,UAAA,iBAAiBC,kBAAc,CAAA,CAAE;AAEvC,UAAM,gBAAgBC,cAAAA,SAAS,MAAM,eAAe,MAAM,SAAS,QAAQ,CAAC;AAEtE,UAAA,YAAYD,kBAAa,KAAK;AAE9B,UAAA,yBAAyBA,kBAAa,KAAK;AAE3C,UAAA,yBAAyBA,kBAAa,KAAK;AAG3C,UAAA,WAAWA,kBAA0B,IAAI;AAIzC,UAAA,gBAAgBC,cAAAA,SAAS,MAAM,EAAE;AAGjC,UAAA,sBAAsBA,cAAAA,SAAS,MAAM,EAAE;AAIvC,UAAA,kBAAkBA,cAAAA,SAAS,OAAO;AAAA,MACtC,aAAa;AAAA,IACb,EAAA;AAGI,UAAA,mBAAmBA,cAAAA,SAAS,OAAO;AAAA,MACvC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,iBAAiB,cAAc,QAAQ,6BAA6B;AAAA,MACpE,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,YAAY;AAAA,IACZ,EAAA;AAGI,UAAA,uBAAuBA,uBAAS,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAM5B;AAGV,UAAA,uBAAuBA,uBAAS,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAM5B;AAOhB,aAAS,sBAAsB,QAAwB;AACrD,qBAAe,QAAQ;AAAA,IACzB;AAKA,aAAS,oBAA0B;AACjC,6BAAuB,QAAQ;AAAA,IACjC;AAKA,aAAS,oBAA0B;AACjC,6BAAuB,QAAQ;AAAA,IACjC;AAOS,aAAA,UAAU,OAAe,OAAiD,QAAc;AAC/F,UAAI,SAAS,OAAO;AAClB,iBAAS,MAAM,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,UACA,UAAU,SAAS,YAAY,OAAO;AAAA,QAAA,CACvC;AAAA,MACH;AAAA,IACF;AAKA,mBAAe,gBAA+B;AAExC,UAAA,CAAC,cAAc,OAAO;AACxB,kBAAU,YAAY,SAAS;AAC/B;AAAA,MACF;AAEA,gBAAU,QAAQ;AAEd,UAAA;AAEF,cAAM,cAAc,MAAM,IAAI,QAAyB,CAAC,SAAS,WAAW;AAC1EC,wBAAAA,MAAI,MAAM;AAAA,YACR,UAAU;AAAA,YACV,SAAS;AAAA,YACT,MAAM;AAAA,UAAA,CACP;AAAA,QAAA,CACF;AAGD,cAAM,cAA2B;AAAA,UAC/B,MAAM,YAAY;AAAA,QAAA;AAId,cAAA,WAAqB,MAAMC,6BAAQ,WAAW;AAGpD,mBAAW,QAAQ;AAGnB,kBAAU,QAAQ,SAAS;AAG3B,mBAAW,MAAM;AACf,+BAAqB,SAAS,MAAM;AAAA,WACnC,IAAI;AAAA,eAEA,OAAO;AACdD,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,KAAK;AACjE,kBAAU,YAAY,OAAO;AAAA,MAAA,UAC7B;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAMA,aAAS,qBAAqB,QAAkC;AAC9D,cAAQ,QAAQ;AAAA,QACd,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,mBAAoB,CAAA;AACxC;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAEHA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAC9C;AAAA,QACF,KAAK;AAAA,QACL;AAEEA,wBAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAClD;AAAA,MACJ;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtSA,GAAG,WAAW,eAAe;"}